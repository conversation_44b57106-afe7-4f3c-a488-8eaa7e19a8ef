/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    Connector: typeof import('./src/components/Connector.vue')['default']
    Helper: typeof import('./src/components/Helper.vue')['default']
    HelperZoom: typeof import('./src/components/HelperZoom.vue')['default']
    'IBx:bxNews': typeof import('~icons/bx/bx-news')['default']
    'IHeroiconsSolid:minusSm': typeof import('~icons/heroicons-solid/minus-sm')['default']
    'IHeroiconsSolid:plusSm': typeof import('~icons/heroicons-solid/plus-sm')['default']
    'IIc:baselineAutoFixHigh': typeof import('~icons/ic/baseline-auto-fix-high')['default']
    'IIc:outlineAccountTree': typeof import('~icons/ic/outline-account-tree')['default']
    'IMajesticons:cogLine': typeof import('~icons/majesticons/cog-line')['default']
    'IMajesticons:moon': typeof import('~icons/majesticons/moon')['default']
    'IMdi:cameraOutline': typeof import('~icons/mdi/camera-outline')['default']
    'IMdi:databaseExportOutline': typeof import('~icons/mdi/database-export-outline')['default']
    'IMdi:deleteOutline': typeof import('~icons/mdi/delete-outline')['default']
    'IMdi:languageTypescript': typeof import('~icons/mdi/language-typescript')['default']
    'IMdi:robot': typeof import('~icons/mdi/robot')['default']
    'IMdi:shareVariantOutline': typeof import('~icons/mdi/share-variant-outline')['default']
    IMdiClipboardOutline: typeof import('~icons/mdi/clipboard-outline')['default']
    IMdiGithub: typeof import('~icons/mdi/github')['default']
    IMdiLink: typeof import('~icons/mdi/link')['default']
    InputText: typeof import('./src/components/InputText.vue')['default']
    'IUil:focusTarget': typeof import('~icons/uil/focus-target')['default']
    Loading: typeof import('./src/components/Loading.vue')['default']
    ModalSQL: typeof import('./src/components/ModalSQL.vue')['default']
    ModalTypes: typeof import('./src/components/ModalTypes.vue')['default']
    Navigation: typeof import('./src/components/Navigation.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Settings: typeof import('./src/components/Settings.vue')['default']
    SQLCard: typeof import('./src/components/SQLCard.vue')['default']
    Table: typeof import('./src/components/Table.vue')['default']
  }
}
