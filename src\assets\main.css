@font-face {
  font-family: custom-font;
  font-weight: 500;
  font-style: normal;
  src: url(./fonts/CustomFont-Medium.woff2) format('woff2');
}

html {
  font-family: custom-font, BlinkMacSystemFont, -apple-system, Segoe UI, Roboto,
    Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, Helvetica,
    Arial, sans-serif;
}

input[type='color'] {
  -webkit-appearance: none;
  border: none;
  overflow: hidden;
  width: 100%;
  height: 100%;
  cursor: pointer;
}
input[type='color']::-webkit-color-swatch-wrapper {
  padding: 0;
}
input[type='color']::-webkit-color-swatch {
  border: none;
}

/* width */
::-webkit-scrollbar {
  width: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  @apply dark:bg-dark-800;
}

/* Handle */
::-webkit-scrollbar-thumb {
  @apply bg-warm-gray-200 dark:bg-dark-100;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #2a2a2a;
}

::-moz-selection {
  @apply text-white bg-green-500;
}
::selection {
  @apply text-white bg-green-500;
}

.btn {
  @apply p-3 flex rounded-md bg-light-300 dark:bg-dark-800 border-2 dark:border-dark-border cursor-pointer hover:bg-light-700 dark:hover:bg-dark-600 focus:outline-none transition duration-300 ease-in;
}

.btn-green {
  @apply bg-green-500 text-white rounded-md px-4 py-0 h-8 text-sm font-medium hover:bg-green-600 focus:outline-none focus:ring focus:ring-green-600;
}
